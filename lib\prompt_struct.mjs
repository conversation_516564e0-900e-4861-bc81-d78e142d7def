/**
 * 提示词结构构建器
 * 本地实现，替代 fount 平台的 prompt_struct 构建器
 */

/** @typedef {import('../types/chatLog.mjs').chatLogEntry_t} chatLogEntry_t */

/**
 * 提示词结构类型
 * @typedef {Object} prompt_struct_t
 * @property {Object[]} messages - 消息列表
 * @property {string} systemPrompt - 系统提示词
 * @property {string} userPrompt - 用户提示词
 * @property {Object[]} context - 上下文信息
 * @property {Object} metadata - 元数据
 * @property {number} totalTokens - 总令牌数估算
 */

/**
 * 消息角色枚举
 */
export const MessageRole = {
	SYSTEM: 'system',
	USER: 'user',
	ASSISTANT: 'assistant',
	FUNCTION: 'function'
}

/**
 * 构建提示词结构
 * @param {Object} args - 构建参数
 * @param {chatLogEntry_t[]} args.chat_log - 聊天日志
 * @param {string} args.systemPrompt - 系统提示词
 * @param {string} args.userPrompt - 用户提示词
 * @param {Object[]} args.context - 上下文信息
 * @param {Object} args.options - 构建选项
 * @returns {prompt_struct_t} 提示词结构
 */
export function buildPromptStruct(args) {
	const {
		chat_log = [],
		systemPrompt = '',
		userPrompt = '',
		context = [],
		options = {}
	} = args

	const {
		maxMessages = 50,
		maxTokens = 4000,
		includeSystemPrompt = true,
		includeContext = true,
		filterEmptyMessages = true
	} = options

	const messages = []
	let totalTokens = 0

	// 添加系统提示词
	if (includeSystemPrompt && systemPrompt) {
		const systemMessage = {
			role: MessageRole.SYSTEM,
			content: systemPrompt,
			timestamp: Date.now()
		}
		messages.push(systemMessage)
		totalTokens += estimateTokens(systemPrompt)
	}

	// 添加上下文信息
	if (includeContext && context.length > 0) {
		for (const contextItem of context) {
			if (contextItem.content) {
				const contextMessage = {
					role: MessageRole.SYSTEM,
					content: contextItem.content,
					timestamp: contextItem.timestamp || Date.now(),
					metadata: { type: 'context', ...contextItem.metadata }
				}
				messages.push(contextMessage)
				totalTokens += estimateTokens(contextItem.content)
			}
		}
	}

	// 处理聊天日志
	const processedChatLog = processChatLog(chat_log, {
		maxMessages,
		maxTokens: maxTokens - totalTokens,
		filterEmptyMessages
	})

	// 添加聊天消息
	for (const logEntry of processedChatLog) {
		const message = convertChatLogToMessage(logEntry)
		if (message) {
			messages.push(message)
			totalTokens += estimateTokens(message.content)
		}
	}

	// 添加用户提示词
	if (userPrompt) {
		const userMessage = {
			role: MessageRole.USER,
			content: userPrompt,
			timestamp: Date.now()
		}
		messages.push(userMessage)
		totalTokens += estimateTokens(userPrompt)
	}

	return {
		messages,
		systemPrompt,
		userPrompt,
		context,
		metadata: {
			messageCount: messages.length,
			chatLogCount: processedChatLog.length,
			contextCount: context.length,
			buildTime: Date.now(),
			options
		},
		totalTokens
	}
}

/**
 * 处理聊天日志
 * @param {chatLogEntry_t[]} chatLog - 聊天日志
 * @param {Object} options - 处理选项
 * @returns {chatLogEntry_t[]} 处理后的聊天日志
 */
function processChatLog(chatLog, options = {}) {
	const {
		maxMessages = 50,
		maxTokens = 2000,
		filterEmptyMessages = true
	} = options

	let processedLog = [...chatLog]

	// 过滤空消息
	if (filterEmptyMessages) {
		processedLog = processedLog.filter(entry => 
			entry.content && entry.content.trim().length > 0
		)
	}

	// 限制消息数量（保留最新的消息）
	if (processedLog.length > maxMessages) {
		processedLog = processedLog.slice(-maxMessages)
	}

	// 限制令牌数量
	if (maxTokens > 0) {
		let currentTokens = 0
		const filteredLog = []

		// 从最新消息开始计算
		for (let i = processedLog.length - 1; i >= 0; i--) {
			const entry = processedLog[i]
			const entryTokens = estimateTokens(entry.content)
			
			if (currentTokens + entryTokens <= maxTokens) {
				filteredLog.unshift(entry)
				currentTokens += entryTokens
			} else {
				break
			}
		}

		processedLog = filteredLog
	}

	return processedLog
}

/**
 * 将聊天日志条目转换为消息格式
 * @param {chatLogEntry_t} logEntry - 聊天日志条目
 * @returns {Object|null} 消息对象
 */
function convertChatLogToMessage(logEntry) {
	if (!logEntry || !logEntry.content) {
		return null
	}

	// 确定消息角色
	let role = MessageRole.USER
	if (logEntry.role) {
		role = logEntry.role
	} else if (logEntry.name && logEntry.name.includes('bot')) {
		role = MessageRole.ASSISTANT
	}

	const message = {
		role,
		content: logEntry.content,
		timestamp: logEntry.time ? new Date(logEntry.time).getTime() : Date.now(),
		metadata: {
			name: logEntry.name,
			platform: logEntry.platform,
			messageId: logEntry.messageId,
			channelId: logEntry.channelId,
			userId: logEntry.userId
		}
	}

	// 添加文件信息
	if (logEntry.files && logEntry.files.length > 0) {
		message.files = logEntry.files.map(file => ({
			name: file.name,
			mimeType: file.mimeType,
			description: file.description,
			size: file.buffer ? file.buffer.length : 0
		}))
	}

	// 添加扩展信息
	if (logEntry.extension) {
		message.metadata.extension = logEntry.extension
	}

	return message
}

/**
 * 估算文本的令牌数量
 * @param {string} text - 文本内容
 * @returns {number} 估算的令牌数
 */
function estimateTokens(text) {
	if (!text || typeof text !== 'string') {
		return 0
	}

	// 简单的令牌估算：
	// - 英文：大约 4 个字符 = 1 个令牌
	// - 中文：大约 1.5 个字符 = 1 个令牌
	const chineseRegex = /[\u4e00-\u9fff]/g
	const chineseMatches = text.match(chineseRegex) || []
	const chineseChars = chineseMatches.length
	const otherChars = text.length - chineseChars

	const chineseTokens = Math.ceil(chineseChars / 1.5)
	const otherTokens = Math.ceil(otherChars / 4)

	return chineseTokens + otherTokens
}

/**
 * 验证提示词结构
 * @param {prompt_struct_t} promptStruct - 提示词结构
 * @returns {Object} 验证结果
 */
export function validatePromptStruct(promptStruct) {
	const errors = []
	const warnings = []

	if (!promptStruct) {
		errors.push('Prompt structure is null or undefined')
		return { valid: false, errors, warnings }
	}

	if (!Array.isArray(promptStruct.messages)) {
		errors.push('Messages must be an array')
	} else if (promptStruct.messages.length === 0) {
		warnings.push('Messages array is empty')
	}

	if (promptStruct.totalTokens > 8000) {
		warnings.push(`High token count: ${promptStruct.totalTokens}`)
	}

	// 检查消息格式
	for (let i = 0; i < promptStruct.messages.length; i++) {
		const message = promptStruct.messages[i]
		if (!message.role) {
			errors.push(`Message ${i} missing role`)
		}
		if (!message.content) {
			warnings.push(`Message ${i} has empty content`)
		}
	}

	return {
		valid: errors.length === 0,
		errors,
		warnings
	}
}

/**
 * 格式化提示词结构为字符串
 * @param {prompt_struct_t} promptStruct - 提示词结构
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的字符串
 */
export function formatPromptStruct(promptStruct, options = {}) {
	const {
		includeMetadata = false,
		includeTimestamps = false,
		maxContentLength = 100
	} = options

	let result = 'Prompt Structure:\n'
	result += `- Messages: ${promptStruct.messages.length}\n`
	result += `- Total Tokens: ${promptStruct.totalTokens}\n`

	if (includeMetadata && promptStruct.metadata) {
		result += `- Metadata: ${JSON.stringify(promptStruct.metadata, null, 2)}\n`
	}

	result += '\nMessages:\n'
	for (let i = 0; i < promptStruct.messages.length; i++) {
		const message = promptStruct.messages[i]
		let content = message.content
		
		if (content.length > maxContentLength) {
			content = content.substring(0, maxContentLength) + '...'
		}

		result += `${i + 1}. [${message.role}] ${content}`
		
		if (includeTimestamps && message.timestamp) {
			result += ` (${new Date(message.timestamp).toISOString()})`
		}
		
		result += '\n'
	}

	return result
}
