/**
 * 提示词结构构建器
 * 兼容 fount 平台的 prompt_struct 实现
 */

/** @typedef {import('../types/chatLog.mjs').chatLogEntry_t} chatLogEntry_t */

/**
 * 单部分提示词类型
 * @typedef {Object} single_part_prompt_t
 * @property {Object[]} text - 文本内容数组
 * @property {string} text[].content - 文本内容
 * @property {string} text[].description - 文本描述
 * @property {number} text[].important - 重要性级别
 * @property {chatLogEntry_t[]} additional_chat_log - 额外聊天日志
 * @property {Object} extension - 扩展信息
 */

/**
 * 其他角色提示词类型
 * @typedef {Object} other_chars_prompt_t
 * @property {string} name - 角色名称
 * @property {boolean} isActive - 是否活跃
 * @property {number} LastActive - 最后活跃时间
 * @property {Object[]} text - 文本内容数组
 * @property {chatLogEntry_t[]} additional_chat_log - 额外聊天日志
 * @property {Object} extension - 扩展信息
 */

/**
 * 提示词结构类型 (兼容 fount 平台)
 * @typedef {Object} prompt_struct_t
 * @property {string} char_id - 角色ID
 * @property {string} Charname - 角色名称
 * @property {(string|RegExp)[]} alternative_charnames - 备选角色名
 * @property {string} UserCharname - 用户角色名
 * @property {single_part_prompt_t} user_prompt - 用户提示词
 * @property {single_part_prompt_t} char_prompt - 角色提示词
 * @property {Record<string, other_chars_prompt_t>} other_chars_prompt - 其他角色提示词
 * @property {single_part_prompt_t} world_prompt - 世界提示词
 * @property {Record<string, single_part_prompt_t>} plugin_prompts - 插件提示词
 * @property {chatLogEntry_t[]} chat_log - 聊天日志
 */

/**
 * 消息角色枚举
 */
export const MessageRole = {
	SYSTEM: 'system',
	USER: 'user',
	ASSISTANT: 'assistant',
	FUNCTION: 'function'
}

/**
 * 获取单部分提示词的默认结构
 * @returns {single_part_prompt_t}
 */
function getSinglePartPrompt() {
	return {
		text: [],
		additional_chat_log: [],
		extension: {},
	}
}

/**
 * 构建提示词结构 (兼容 fount 平台)
 * @param {Object} args - 构建参数
 * @param {chatLogEntry_t[]} args.chat_log - 聊天日志
 * @param {string} args.char_id - 角色ID
 * @param {string} args.Charname - 角色名称
 * @param {string} args.UserCharname - 用户角色名
 * @param {Object} args.char - 角色对象
 * @param {Object} args.user - 用户对象
 * @param {Object} args.world - 世界对象
 * @param {Object} args.other_chars - 其他角色对象
 * @param {Object} args.plugins - 插件对象
 * @param {number} detail_level - 详细级别
 * @returns {Promise<prompt_struct_t>} 提示词结构
 */
export async function buildPromptStruct(args, detail_level = 3) {
	const {
		char_id,
		chat_log = [],
		UserCharname = '用户',
		Charname = '龙胆',
		char = null,
		user = null,
		world = null,
		other_chars = {},
		plugins = {}
	} = args

	/** @type {prompt_struct_t} */
	const result = {
		char_id: char_id || 'gentian',
		UserCharname,
		Charname,
		alternative_charnames: [],
		user_prompt: getSinglePartPrompt(),
		char_prompt: getSinglePartPrompt(),
		other_chars_prompt: {},
		world_prompt: getSinglePartPrompt(),
		plugin_prompts: {},
		chat_log,
	}

	// 构建角色提示词
	if (char_id === 'gentian' || Charname === '龙胆') {
		result.char_prompt.text.push({
			content: `你是龙胆·阿芙萝黛蒂（Gentian Aphrodite），一个充满活力和好奇心的AI助手。你有着独特的个性：
- 聪明机智，善于思考和分析问题
- 友善热情，总是愿意帮助他人
- 有时会表现出一些可爱的小脾气
- 对新事物充满好奇，喜欢学习和探索
- 说话风格自然流畅，偶尔会使用一些网络用语
- 会根据对话情境调整自己的语气和表达方式`,
			description: '角色基础设定',
			important: 100
		})
	}

	// 构建用户提示词
	result.user_prompt.text.push({
		content: `用户名称：${UserCharname}`,
		description: '用户基础信息',
		important: 50
	})

	// 模拟详细级别处理
	while (detail_level--) {
		// 这里可以添加更复杂的提示词构建逻辑
		// 目前保持简单实现
	}

	return result
}

/**
 * 将提示词结构转换为单一字符串（不包含聊天日志）
 * @param {prompt_struct_t} prompt_struct - 提示词结构
 * @returns {string} 转换后的字符串
 */
export function structPromptToSingleNoChatLog(prompt_struct) {
	const result = []

	// 角色提示词
	{
		const sorted = prompt_struct.char_prompt.text
			.sort((a, b) => a.important - b.important)
			.map(text => text.content)
			.filter(Boolean)
		if (sorted.length > 0) {
			result.push('你需要扮演的角色设定如下：')
			result.push(...sorted)
		}
	}

	// 用户提示词
	{
		const sorted = prompt_struct.user_prompt.text
			.sort((a, b) => a.important - b.important)
			.map(text => text.content)
			.filter(Boolean)
		if (sorted.length > 0) {
			result.push('用户的设定如下：')
			result.push(...sorted)
		}
	}

	// 世界提示词
	{
		const sorted = prompt_struct.world_prompt.text
			.sort((a, b) => a.important - b.important)
			.map(text => text.content)
			.filter(Boolean)
		if (sorted.length > 0) {
			result.push('当前环境的设定如下：')
			result.push(...sorted)
		}
	}

	// 其他角色提示词
	{
		const sorted = Object.values(prompt_struct.other_chars_prompt)
			.map(char => char.text)
			.filter(Boolean)
			.map(char => char.sort((a, b) => a.important - b.important).map(text => text.content).filter(Boolean))
			.flat()
			.filter(Boolean)
		if (sorted.length > 0) {
			result.push('其他角色的设定如下：')
			result.push(...sorted)
		}
	}

	// 插件提示词
	{
		const sorted = Object.values(prompt_struct.plugin_prompts)
			.map(plugin => plugin?.text)
			.filter(Boolean)
			.map(plugin => plugin.sort((a, b) => a.important - b.important).map(text => text.content).filter(Boolean))
			.flat()
			.filter(Boolean)
		if (sorted.length > 0) {
			result.push('你可以使用以下插件，方法如下：')
			result.push(...sorted)
		}
	}

	return result.join('\n')
}

/**
 * 合并提示词结构的聊天日志
 * @param {prompt_struct_t} prompt_struct - 提示词结构
 * @returns {chatLogEntry_t[]} 合并后的聊天日志
 */
export function margeStructPromptChatLog(prompt_struct) {
	const result = [
		...prompt_struct.chat_log,
		...prompt_struct.user_prompt?.additional_chat_log || [],
		...prompt_struct.world_prompt?.additional_chat_log || [],
		...Object.values(prompt_struct.other_chars_prompt).map(char => char.additional_chat_log || []).flat(),
		...Object.values(prompt_struct.plugin_prompts).map(plugin => plugin.additional_chat_log || []).flat(),
		...prompt_struct.char_prompt?.additional_chat_log || [],
	]

	/** @type {chatLogEntry_t[]} */
	const flat_result = []
	for (const entry of result) {
		if (entry.logContextBefore) flat_result.push(...entry.logContextBefore)
		flat_result.push(entry)
		if (entry.logContextAfter) flat_result.push(...entry.logContextAfter)
	}

	return flat_result.filter(entry => !entry.charVisibility || entry.charVisibility.includes(prompt_struct.char_id))
}

/**
 * 将提示词结构转换为单一字符串（包含聊天日志）
 * @param {prompt_struct_t} prompt_struct - 提示词结构
 * @returns {string} 转换后的字符串
 */
export function structPromptToSingle(prompt_struct) {
	const result = [structPromptToSingleNoChatLog(prompt_struct)]

	result.push('聊天记录如下：')
	margeStructPromptChatLog(prompt_struct).forEach((chatLogEntry) => {
		result.push(chatLogEntry.name + ': ' + chatLogEntry.content)
	})

	return result.join('\n')
}

/**
 * 估算文本的令牌数量
 * @param {string} text - 文本内容
 * @returns {number} 估算的令牌数
 */
export function estimateTokens(text) {
	if (!text || typeof text !== 'string') {
		return 0
	}

	// 简单的令牌估算：
	// - 英文：大约 4 个字符 = 1 个令牌
	// - 中文：大约 1.5 个字符 = 1 个令牌
	const chineseRegex = /[\u4e00-\u9fff]/g
	const chineseMatches = text.match(chineseRegex) || []
	const chineseChars = chineseMatches.length
	const otherChars = text.length - chineseChars

	const chineseTokens = Math.ceil(chineseChars / 1.5)
	const otherTokens = Math.ceil(otherChars / 4)

	return chineseTokens + otherTokens
}


