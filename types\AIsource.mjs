/**
 * AI 源类型定义
 * 基于 fount 平台的 AIsource_t 接口重新实现
 */

/**
 * AI 源配置类型
 * @typedef {Object} AISourceConfig_t
 * @property {string} name - AI 源名称
 * @property {string} type - AI 源类型 (openai, anthropic, local, etc.)
 * @property {string} endpoint - API 端点
 * @property {string} apiKey - API 密钥
 * @property {string} model - 模型名称
 * @property {Object} parameters - 模型参数
 * @property {number} maxTokens - 最大令牌数
 * @property {number} temperature - 温度参数
 * @property {number} topP - Top-P 参数
 * @property {boolean} enabled - 是否启用
 */

/**
 * AI 源响应类型
 * @typedef {Object} AISourceResponse_t
 * @property {string} content - 响应内容
 * @property {Object[]} files - 附件文件列表
 * @property {Buffer} files[].buffer - 文件缓冲区
 * @property {string} files[].name - 文件名
 * @property {string} files[].mimeType - MIME 类型
 * @property {string} files[].description - 文件描述
 * @property {Object} metadata - 元数据
 * @property {number} tokensUsed - 使用的令牌数
 * @property {string} model - 使用的模型
 * @property {number} timestamp - 时间戳
 */

/**
 * AI 源请求类型
 * @typedef {Object} AISourceRequest_t
 * @property {string} prompt - 提示词
 * @property {Object[]} messages - 消息历史
 * @property {string} messages[].role - 消息角色 (user, assistant, system)
 * @property {string} messages[].content - 消息内容
 * @property {Object[]} files - 附件文件
 * @property {Object} parameters - 请求参数
 * @property {number} maxTokens - 最大令牌数
 * @property {number} temperature - 温度参数
 * @property {boolean} stream - 是否流式响应
 */

/**
 * AI 源状态类型
 * @typedef {Object} AISourceStatus_t
 * @property {boolean} available - 是否可用
 * @property {string} status - 状态描述
 * @property {number} lastUsed - 最后使用时间
 * @property {number} requestCount - 请求计数
 * @property {number} errorCount - 错误计数
 * @property {string} lastError - 最后错误信息
 */

/**
 * AI 源主接口类型
 * @typedef {Object} AIsource_t
 * @property {string} filename - 文件名
 * @property {string} name - 显示名称
 * @property {string} description - 描述
 * @property {AISourceConfig_t} config - 配置
 * @property {AISourceStatus_t} status - 状态
 * @property {Function} call - 调用 AI 源
 * @property {Function} test - 测试连接
 * @property {Function} getInfo - 获取信息
 * @property {Function} updateConfig - 更新配置
 * @property {Function} reset - 重置状态
 */

/**
 * AI 源调用选项类型
 * @typedef {Object} AISourceCallOptions_t
 * @property {number} retries - 重试次数
 * @property {number} timeout - 超时时间
 * @property {Function} onProgress - 进度回调
 * @property {Function} onError - 错误回调
 * @property {boolean} fallback - 是否允许回退
 */

/**
 * AI 源管理器类型
 * @typedef {Object} AISourceManager_t
 * @property {Map<string, AIsource_t>} sources - AI 源映射
 * @property {Function} load - 加载 AI 源
 * @property {Function} unload - 卸载 AI 源
 * @property {Function} get - 获取 AI 源
 * @property {Function} list - 列出所有 AI 源
 * @property {Function} call - 调用 AI 源
 * @property {Function} testAll - 测试所有 AI 源
 */

/**
 * AI 源错误类型
 * @typedef {Error} AISourceError_t
 * @property {string} code - 错误代码
 * @property {string} source - 错误来源
 * @property {Object} details - 错误详情
 */

export {}
